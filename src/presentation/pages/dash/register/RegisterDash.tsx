import DashInscriptionForm from "@/presentation/components/features/dash/DashInscriptionForm.tsx";
import useVerifyDashToken from "@/presentation/hooks/dash/use-verify-dash-token";
import { useToast } from "@/presentation/hooks/use-toast.ts";
import { PublicRoutesNavigation } from "@/shared/constants/AppRoutesNavigation.ts";
import { useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";

const RegisterDash = () => {
  const { token } = useParams();
  const toast = useToast();
  const navigate = useNavigate();

  const { isLoading, isTokenValid, error, invitationData, setError } =
    useVerifyDashToken(token);

  useEffect(() => {
    if (error) {
      toast.error(error);
      setError("");
    }
  }, [error]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!isTokenValid && !isLoading) {
    navigate(PublicRoutesNavigation.MAIN_PAGE);
    return;
  }

  if (invitationData && !isLoading && isTokenValid) {
    return (
      <DashInscriptionForm
        invitationId={invitationData?.id}
        isInvitationUsed={invitationData?.est_utilisee}
        defaultValue={invitationData}
      />
    );
  }
};

export default RegisterDash;
