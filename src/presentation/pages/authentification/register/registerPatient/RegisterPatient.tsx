import UnathenticatedLayout from "@/presentation/components/layouts/UnauthenticatedLayout";
import { motion } from "framer-motion";
import { FormStepperWrapper } from "@/presentation/components/common/ScrollToTop";
import { usePatientRegistrationLogic } from "@/presentation/hooks/authentification/patient";
import RegisterPatientInformations from "@/presentation/components/features/patient/registerPatienStepper/RegisterPatientInformations";
import RegisterPatientForm from "@/presentation/components/features/patient/registerPatienStepper/RegisterPatientForm";
import RegisterPatientCurrentStepIndicator from "@/presentation/components/features/patient/registerPatienStepper/RegisterPatientCurrentStepIndicator";
import RegisterPatientTitle from "@/presentation/components/features/patient/registerPatienStepper/RegisterPatientTitle";

/**
 * Page principale d'inscription patient avec architecture modulaire
 *
 * Ce composant orchestre l'ensemble du processus d'inscription patient en utilisant
 * une architecture modulaire composée de plusieurs sous-composants spécialisés.
 * Il utilise le hook usePatientRegistrationLogic pour gérer toute la logique métier
 * et délègue l'affichage à des composants dédiés.
 *
 * @component
 * @page
 * @route /register/patient
 *
 * @example
 * ```tsx
 * // Utilisation dans le routeur
 * <Route path="/register/patient" element={<RegisterPatient />} />
 * ```
 *
 * @returns {JSX.Element} Page complète d'inscription patient avec layout non-authentifié
 *
 * @architecture
 * La page est composée de 4 sous-composants principaux :
 * - RegisterPatientTitle: Titre et description de la page
 * - RegisterPatientCurrentStepIndicator: Indicateur de progression
 * - RegisterPatientInformations: Panneau d'informations latéral
 * - RegisterPatientForm: Formulaire multi-étapes principal
 *
 * @features
 * - Architecture modulaire et réutilisable
 * - Séparation claire des responsabilités
 * - Gestion centralisée de la logique métier
 * - Design responsive avec layout en deux colonnes
 * - Intégration complète avec le système d'authentification
 *
 * @dependencies
 * - usePatientRegistrationLogic: Hook principal pour la logique métier
 * - UnathenticatedLayout: Layout pour les pages non-authentifiées
 * - FormStepperWrapper: Wrapper pour la gestion du scroll
 * - Sous-composants modulaires pour l'affichage
 *
 */
const RegisterPatient = (): JSX.Element => {
  // Utilisation du hook principal qui orchestre toute la logique
  const {
    activeStep,
    control,
    errors,
    register,
    setValue,
    handleSubmit,
    isLoading,
    handleNextStep,
    handlePreviousStep,
    onSubmit,
  } = usePatientRegistrationLogic();
  const isLoginUser = true;
  return (
    <UnathenticatedLayout>
      <FormStepperWrapper
        activeStep={activeStep}
        config={{
          behavior: "smooth",
          delay: 200,
          top: 0,
        }}
      >
        {/* Fond dégradé pour toute la page */}
        <div className="min-h-screen bg-gradient-to-br from-blue-400 via-teal-400 to-green-400 py-12 md:px-4">
          <div className="md:container md:mx-auto max-w-6xl">
            {/* Titre principal centré */}
            <RegisterPatientTitle />

            {/* Stepper moderne */}
            <RegisterPatientCurrentStepIndicator activeStep={activeStep} />

            {/* Carte principale avec design contact */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-white rounded-3xl shadow-2xl overflow-hidden"
            >
              <div className="flex flex-col lg:flex-row min-h-[600px]">
                {/* Section gauche - Information */}
                <RegisterPatientInformations activeStep={activeStep} />

                {/* Section droite - Formulaire */}
                <RegisterPatientForm
                  onNext={handleNextStep}
                  onBack={handlePreviousStep}
                  activeStep={activeStep}
                  control={control}
                  errors={errors}
                  isDisabled={isLoading}
                  register={register}
                  setValue={setValue}
                  onSubmit={handleSubmit(() => onSubmit(isLoginUser))}
                />
              </div>
            </motion.div>
          </div>
        </div>
      </FormStepperWrapper>
    </UnathenticatedLayout>
  );
};

export default RegisterPatient;
