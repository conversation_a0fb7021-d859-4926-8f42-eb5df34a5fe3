import { Building, Lock, Mail } from "lucide-react";
import Form<PERSON>ield from "../../common/ui/FormField.tsx";
import useDashForm from "@/presentation/hooks/dash/use-dash-form.ts";
import { DashInvitationFormData } from "@/shared/schemas/DashInvitationSchema.ts";
import Button from "../../common/Button/Button.tsx";

interface DashInscriptionFormProps {
  invitationId: number;
  defaultValue?: DashInvitationFormData;
  isInvitationUsed: boolean;
}

const DashInscriptionForm = ({
  invitationId,
  defaultValue,
  isInvitationUsed,
}: DashInscriptionFormProps) => {
  const { register, errors, handleSubmit, getValues, isLoading } = useDashForm(
    invitationId,
    defaultValue
  );
  return (
    <form
      onSubmit={handleSubmit}
      className="flex flex-col text-center justify-center items-center min-h-screen"
    >
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 md:gap-5">
        <FormField
          id="organizationName"
          placeholder="Entrez votre nom d'organisme"
          type="text"
          required
          icon={Building}
          value={defaultValue?.organizationName || ""}
          label="Nom de l'organisme"
          register={register}
          error={errors.organizationName}
          validation={{
            required: "Le nom de l'organisme est requis",
            minLength: {
              value: 2,
              message:
                "Le nom de l'organisme doit contenir au moins 2 caractères",
            },
            maxLength: {
              value: 100,
              message:
                "Le nom de l'organisme ne peut pas dépasser 100 caractères",
            },
            pattern: {
              value: /^[a-zA-ZÀ-ÿ0-9\s\-_.&()]+$/,
              message:
                "Le nom de l'organisme ne peut contenir que des lettres, chiffres, espaces et caractères spéciaux autorisés (- _ . & ( ))",
            },
          }}
        />
        <FormField
          id="email"
          placeholder="Entrez votre email"
          type="email"
          required
          icon={Mail}
          value={defaultValue?.email || ""}
          label="Email"
          register={register}
          error={errors.email}
          validation={{
            required: "L'email est requis",
            pattern: {
              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
              message: "Adresse email invalide",
            },
          }}
        />
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 md:gap-5">
        <FormField
          id="password"
          placeholder="Entrez votre mot de passe"
          type="password"
          required
          icon={Lock}
          label="Mot de passe"
          showPasswordStrength
          register={register}
          error={errors.password}
          validation={{
            required: "Le mot de passe est requis",
            minLength: {
              value: 8,
              message: "Le mot de passe doit contenir au moins 8 caractères",
            },
          }}
        />
        <FormField
          id="confirmPassword"
          placeholder="Confirmez votre mot de passe"
          type="password"
          required
          icon={Lock}
          label="Confirmer le mot de passe"
          register={register}
          error={errors.confirmPassword}
          validation={{
            required: "La confirmation du mot de passe est requise",
            validate: (value) =>
              value === getValues("password") ||
              "Les mots de passe ne correspondent pas",
          }}
        />
      </div>

      <Button type="submit" disabled={isLoading || isInvitationUsed}>
        {isLoading ? "Chargement..." : "S'inscrire"}
      </Button>
    </form>
  );
};

export default DashInscriptionForm;
